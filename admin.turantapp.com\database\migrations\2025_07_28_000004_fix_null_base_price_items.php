<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update items where base_price is NULL to use the price value
        // This fixes items created before the base_price column was added
        DB::table('items')
            ->whereNull('base_price')
            ->whereNotNull('price')
            ->update(['base_price' => DB::raw('price')]);
            
        // Also update temp_products table
        DB::table('temp_products')
            ->whereNull('base_price')
            ->whereNotNull('price')
            ->update(['base_price' => DB::raw('price')]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this fix
    }
};
