{"suffix_name": "Vendor App", "sign_in": "Sign In", "email": "E-mail", "password": "Password", "confirm_password": "Confirm Password", "remember_me": "Remember me", "forgot_password": "Forgot password", "phone": "Phone", "enter_email_address": "Enter your email address", "enter_a_valid_email_address": "Enter a valid email address", "enter_phone_number": "Enter your phone number", "enter_a_valid_phone_number": "Enter a valid phone number", "enter_password": "Enter your password", "password_does_not_matched": "Password doesn't matched", "password_should_be": "Password should be greater than 8 character", "otp_verification": "OTP Verification", "enter_the_verification_sent_to": "Enter the verification sent to", "verify": "Verify", "did_not_receive_the_code": "Didn't receive the code?", "resent_it": "Resend It", "verified": "Verified", "resent_code_successful": "Resent code successful", "please_enter_email": "Please enter your registered email address so we can help you recover your password.", "done": "Done", "enter_new_password": "Enter your new password and confirm password.", "new_password": "New Password", "reset_password": "Reset Password", "order_id": "Order ID", "cash_on_delivery": "Cash On Delivery", "digital_payment": "Digital Payment", "paid": "Paid", "unpaid": "Unpaid", "pending": "Pending", "accepted": "Accepted", "running": "Running", "paused": "Paused", "processing": "Processing", "confirmed": "Confirmed", "handover": "Ready For Handover", "picked_up": "Delivery Item Is On The Way", "delivered": "Delivered", "refund_requested": "Refund Requested", "refunded": "Refunded", "returned": "Returned", "failed": "Payment Failed", "canceled": "Canceled", "scheduled": "Scheduled", "delivered_at": "Delivered at", "payment_info": "Payment Info", "status": "Status", "method": "Method", "item": "<PERSON><PERSON>", "items": "Items", "no": "No", "yes": "Yes", "no_order_found": "No order found", "my_orders": "My Orders", "order_amount": "Order Amount", "order_details": "Order Details", "total_amount": "Total Amount", "ok": "Ok", "since_joining": "Since Joining", "days": "Days", "total_order": "Total Order", "dark_mode": "Dark Mode", "notification": "Notification", "change_password": "Change Password", "profile_settings": "Profile Settings", "profile": "Profile", "first_name": "First name", "last_name": "Last name", "enter_your_first_name": "Enter your first name", "enter_your_last_name": "Enter your last name", "update": "Update", "no_notification_found": "No notification found", "change_something_to_update": "Change something to update", "logout": "Logout", "are_you_sure_to_logout": "Are you sure you want to logout from here?", "language": "Language", "choose_the_language": "<PERSON>ose The Language", "find_language": "Find language", "save": "Save", "select_language": "Select Language", "select_a_language": "Select a language", "you_can_change_language": "*You can change language later from menu bar", "today": "Today", "this_week": "This Week", "this_month": "This Month", "cooking": "Cooking", "ready_for_handover": "Ready For Handover", "food_on_the_way": "Delivery Item Is On The Way", "campaign_order": "Campaign Order", "order_history": "Order History", "addons": "Addons", "variations": "Variations", "additional_note": "Additional Note", "delivery_man_details": "Delivery Man Details", "customer_details": "Customer Details", "call": "Call", "item_price": "<PERSON><PERSON>", "vat_tax": "Vat/Tax", "subtotal": "Subtotal", "discount": "Discount", "coupon_discount": "Coupon Discount", "delivery_fee": "Delivery Fee", "swipe_to_cooking": "Swipe to Cooking", "swipe_to_process": "Swipe to Process", "swipe_if_ready_for_handover": "Swipe if Ready for Handover", "campaign": "Campaign", "wallet": "Wallet", "transaction": "Transaction", "restaurant": "Restaurant", "store": "Store", "bank_info": "Bank Info", "for_demo_purpose": "For demo purpose please use 1234 as OTP", "edit_profile": "Edit Profile", "scheduled_at": "Scheduled At", "delivery": "Delivery", "take_away": "Take Away", "quantity": "Quantity", "direction": "Direction", "bank_name": "Bank Name", "branch_name": "Branch Name", "holder_name": "Holder Name", "account_no": "Account No", "add_bank": "Add Bank", "edit": "Edit", "currently_no_bank_account_added": "Currently you have not yet added any bank account. Please add bank account", "wallet_amount": "<PERSON><PERSON><PERSON><PERSON> Amount", "withdraw": "Withdraw", "pending_withdraw": "Pending Withdraw", "withdrawn": "Withdrawn", "collected_cash_from_customer": "Collected Cash from Customer", "total_earning": "Total Earning", "withdraw_history": "Withdraw History", "view_all": "View All", "transferred_to": "Transferred to", "success": "Success", "enter_bank_name": "Enter bank name", "enter_branch_name": "Enter branch name", "enter_holder_name": "Enter account holder name", "enter_account_no": "Enter your bank account number", "bank_info_updated": "Bank info updated", "no_withdraw_history_found": "No withdraw history found", "enter_amount": "Enter Amount", "request_sent_successfully": "Request sent successfully", "all": "All", "approved": "Approved", "denied": "Denied", "free_delivery": "Free Delivery", "free": "Free", "closed_now": "Closed Now", "not_available_now": "Not Available Now", "not_available_now_break": "Not Available \nNow", "no_item_available": "No item available", "minimum_purchase": "Minimum Purchase", "maximum_discount": "Maximum Discount", "enjoy": "<PERSON><PERSON>", "off_on_all_items": "off on all items", "off_on_all_foods": "off on all foods", "off": "OFF", "join_now": "Join Now", "leave_now": "Leave Now", "joined": "Joined", "no_campaign_available": "No campaign available", "no_description_found": "No description found", "successfully_joined": "Successfully joined", "successfully_leave": "Leave from campaign successful", "are_you_sure_to_join": "Are you sure want to join this campaign?", "are_you_sure_to_leave": "Are you sure want to leave from this campaign?", "date": "Date", "non_changeable": "Non changeable", "password_updated_successfully": "Password updated successfully", "profile_updated_successfully": "Profile updated successfully", "add_item": "Add Item", "update_item": "Update Item", "item_name": "Item Name", "price": "Price", "base_price": "Base Price", "mrp_price": "MRP Price", "discount_type": "Discount Type", "percent": "Percent", "amount": "Amount", "category": "Category", "sub_category": "Sub Category", "attribute": "Attribute", "add": "Add", "variant": "<PERSON><PERSON><PERSON>", "variant_price": "<PERSON><PERSON><PERSON>", "available_time_starts": "Available Time Starts", "available_time_ends": "Available Time Ends", "description": "Description", "item_image": "Item Image", "submit": "Submit", "categories": "Categories", "restaurant_settings": "Restaurant Settings", "store_settings": "Store Settings", "logo": "Logo", "restaurant_name": "Restaurant Name", "store_name": "Store Name", "contact_number": "Contact Number", "address": "Address", "minimum_order_amount": "Minimum Order Amount", "open_time": "Open Time", "close_time": "Close Time", "schedule_order": "Schedule Order", "cover_photo": "Cover Photo", "enter_a_variant_name": "Enter a variant name", "no_variant_added_yet": "No variant added yet", "no_category_found": "No category found", "no_subcategory_found": "No subcategory found", "enter_your_restaurant_name": "Enter your restaurant name", "enter_your_store_name": "Enter your store name", "enter_restaurant_contact_number": "Enter restaurant contact number", "enter_store_contact_number": "Enter store contact number", "enter_restaurant_address": "Enter restaurant address", "enter_store_address": "Enter store address", "enter_minimum_order_amount": "Enter minimum order amount", "restaurant_settings_updated_successfully": "Restaurant settings updated successfully", "store_settings_updated_successfully": "Store settings updated successfully", "addon_added_successfully": "<PERSON><PERSON> added successfully", "addon_updated_successfully": "Addon updated successfully", "addon_removed_successfully": "<PERSON><PERSON> removed successfully", "no_addon_found": "No addon found", "enter_addon_name": "Enter addon name", "enter_addon_price": "Enter addon price", "addon_name": "Addon Name", "delete": "Delete", "enter_item_name": "Enter item name", "enter_item_price": "Enter item price", "enter_base_price": "Enter base price", "enter_mrp_price": "Enter MRP price", "enter_item_discount": "Enter item discount", "select_a_category": "Select a category", "upload_item_image": "Upload item image", "add_at_least_one_variant_for_every_attribute": "Add at least one variant for every attribute", "enter_price_for_every_variant": "Enter price for every variant", "product_added_successfully": "Product added successfully", "product_updated_successfully": "Product updated successfully", "product_deleted_successfully": "Product deleted successfully", "are_you_sure_want_to_delete_this_product": "Are you sure want to delete this product?", "swipe_to_deliver_order": "Swipe to Deliver Order", "swipe_to_confirm_order": "Swipe to Confirm Order", "are_you_sure_to_confirm": "Are you sure to confirm?", "you_want_to_confirm_this_order": "You want to confirm this order?", "only_for_restaurant_owner": "Only for restaurant owner", "only_for_store_owner": "Only for store owner", "all_items": "All Items", "reviews": "Reviews", "no_review_found": "No review found", "this_feature_is_blocked_by_admin": "You don't have permission for this feature please contact with admin.", "item_details": "<PERSON><PERSON>", "daily_time": "Daily Time", "ratings": "Ratings", "delivery_verification_code": "Delivery verification code", "collect_otp_from_customer": "Collect otp from customer and verify your delivery", "collect_money_from_customer": "Collect money from customer", "order_placed": "Order Placed", "new_order_placed": "A new order has been placed. Please take a look.", "available": "Available", "unavailable": "Unavailable", "item_status_updated_successfully": "Item status updated successfully", "next": "Next", "restaurant_temporarily_closed": "Restaurant Temporarily Closed", "store_temporarily_closed": "Store Temporarily Closed", "are_you_sure_to_open_restaurant": "Are you sure want to change the active status to open for this restaurant?", "are_you_sure_to_open_store": "Are you sure want to change the active status to open for this store?", "are_you_sure_to_close_restaurant": "Are you sure want to change the active status to close for this restaurant temporary?", "are_you_sure_to_close_store": "Are you sure want to change the active status to close for this store temporary?", "item_is_on_the_way": "<PERSON>em is on the way", "gst": "GST", "enter_gst_code": "Enter GST code", "weekly_off_day": "Weekly OffDay", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "pos": "POS", "search_item": "Search item...", "order_now": "Order Now", "one_or_more_product_unavailable": "One or more product in your item list are unavailable now", "item_added": "<PERSON><PERSON> added", "item_updated": "Item updated", "enter_delivery_fee": "Enter delivery fee", "delivery_man": "Delivery Man", "add_delivery_man": "Add Delivery Man", "update_delivery_man": "Update Delivery Man", "enter_delivery_man_first_name": "Enter delivery man first name", "enter_delivery_man_last_name": "Enter delivery man last name", "enter_delivery_man_email_address": "Enter delivery man email address", "enter_delivery_man_phone_number": "Enter delivery man phone number", "enter_password_for_delivery_man": "Enter password for delivery man", "enter_delivery_man_identity_number": "Enter delivery man identity number", "upload_delivery_man_image": "Upload delivery man image", "delivery_man_added_successfully": "Delivery man added successfully", "delivery_man_updated_successfully": "Delivery man updated successfully", "delivery_man_deleted_successfully": "Delivery man deleted successfully", "delivery_man_suspended_successfully": "Delivery man suspended successfully", "delivery_man_unsuspended_successfully": "Delivery man unsuspended successfully", "no_delivery_man_found": "No delivery man found", "passport": "Passport", "driving_license": "Driving License", "nid": "NID", "identity_type": "Identity Type", "identity_number": "Identity Number", "enter_identity_number": "Enter identity number", "delivery_man_image": "Delivery Man Image", "identity_images": "Identity Images", "previously_added": "Previously added", "are_you_sure_want_to_delete_this_delivery_man": "Are you sure want to delete this delivery man?", "online": "Online", "offline": "Offline", "total_delivered_order": "Total Delivered Order", "cash_in_hand": "Cash in Hand", "total_earnings": "Total Earnings", "suspend_this_delivery_man": "Suspend This Delivery Man", "un_suspend_this_delivery_man": "Un-suspend This Delivery Man", "are_you_sure_want_to_suspend_this_delivery_man": "Are you sure want to suspend this delivery man?", "are_you_sure_want_to_un_suspend_this_delivery_man": "Are you sure want to un-suspend this delivery man?", "id": "ID", "privacy_policy": "Privacy Policy", "terms_condition": "Terms & Condition", "swipe_if_out_for_delivery": "Swipe if Out for Delivery", "enter_data_for_english": "Enter item name and description for English language", "join_as": "Join as", "veg": "Veg", "non_veg": "Non-Veg", "item_type": "Item Type", "select_at_least_one_item_type": "Select at least one item type", "confirm": "Confirm", "cancel": "Cancel", "are_you_sure_to_cancel": "Are you sure to cancel?", "you_want_to_cancel_this_order": "You want to cancel this order?", "version": "Version", "we_are_under_maintenance": "We Are Under Maintenance", "we_will_be_right_back": "We Will Be Right Back", "daily_schedule_time": "Daily Schedule Time", "schedule_added_successfully": "Schedule added successfully", "schedule_removed_successfully": "Schedule removed successfully", "schedule_for": "Schedule for", "closing_time_must_be_after_the_opening_time": "Closing time must be after the opening time", "are_you_sure_to_delete_this_schedule": "Are you sure to delete this schedule", "this_schedule_is_overlapped": "This schedule is overlapped with another schedule", "pick_start_time": "Pick start time", "pick_end_time": "Pick end time", "pick_time": "Pick Time", "prescription": "Prescription", "minimum_processing_time": "Minimum Processing Time (Minute)", "enter_minimum_processing_time": "Enter minimum Processing Time", "unit": "Unit", "add_an_unit": "Add an unit", "images": "Images", "stock": "Stock", "total_stock": "Total Stock", "enter_stock_for_every_variant": "Enter stock for every variant", "enter_stock": "Enter stock", "select": "Select", "image": "Image", "item_images": "Item Images", "thumbnail_image": "Thumbnail Image", "maximum_image_limit_is_6": "Maximum image limit is 6", "max_size_2_mb": "Max size 2MB", "approximate_delivery_time": "Approximate delivery time", "minimum": "Minimum", "maximum": "Maximum", "min": "Minutes", "hours": "Hours", "enter_minimum_delivery_time": "Enter minimum delivery time", "enter_maximum_delivery_time": "Enter maximum delivery time", "select_delivery_time_type": "Select delivery time type", "street_number": "Street Number", "house": "House", "floor": "Floor", "your_account_remove_successfully": "Your account remove successfully", "there_is_a_problem_on_removing_your_account": "There is a problem on removing your account", "delete_account": "Delete Account", "are_you_sure_to_delete_account": "Are you sure to delete your account?", "it_will_remove_your_all_information": "It will remove your all information.", "customer_not_found": "Customer not found", "wallet_payment": "Wallet Payment", "delivery_man_tips": "Delivery Man Tips", "write_somethings": "Write Something", "type_here": "Type here..", "user_name": "User Name", "chat": "Cha<PERSON>", "your_chat_list": "Your Chat List", "no_chat_found": "No Chat Found", "conversation_list": "Conversation List", "user": "Customer", "type": "Type", "receiver_name": "Receiver Name", "can_not_launch": "Can Not Launch", "join_as_a_restaurant": "Join As a Restaurant", "restaurant_application": "Restaurant Application", "restaurant_address": "Restaurant Address", "minimum_delivery_time": "Minimum Delivery Time", "maximum_delivery_time": "Maximum Delivery Time", "owner_information": "Owner Information", "login_information": "Login Information", "restaurant_registration": "Restaurant Registration", "enter_restaurant_name": "Enter restaurant name", "enter_vat_amount": "Enter VAT amount", "enter_your_phone_number": "Enter Your Phone Number", "enter_your_email_address": "Enter Your Email Address", "confirm_password_does_not_matched": "Confirm Password Does Not Matched", "latitude": "Latitude", "longitude": "Longitude", "set_location": "Set Location", "select_restaurant_logo": "Select Restaurant Logo", "select_restaurant_cover_photo": "Select Restaurant Cover Photo", "set_restaurant_location": "Set Restaurant Location", "conversation": "Conversation", "restaurant_registration_successful": "Restaurant Registration Successfully", "zone": "Zone", "service_not_available_in_this_area": "Service Not Available In This Area", "search": "Search", "search_location": "Search Location", "not_found": "Not Found", "deleted": "Deleted", "customer": "Customer", "no_conversation_found": "No Conversation Found", "maximum_delivery_time_can_not_be_smaller_then_minimum_delivery_time": "Maximum Delivery Time Can Not Be Smaller Then Minimum Delivery Time", "you_do_not_send_more_then_3_photos": "You can't send more then 3 photos at a time", "select_module": "Select module", "not_available_module": "Not available module", "store_registration": "Store Registration", "store_address": "Store Address", "minute": "Minute", "set_your_store_location": "Set your store location", "select_your_store_zone": "Set your store Zone", "delivery_time_type": "Delivery time type", "enter_store_name": "Enter store name", "select_store_logo": "Select store logo", "select_store_cover_photo": "Select store cover photo", "set_store_location": "Select store location", "delivery_charge_per_km": "Delivery Charge Per km", "enter_delivery_charge_per_km": "Enter Delivery Charge Per km", "store_owner": "Store Owner", "store_employee": "Store Employee", "you_have_no_permission_to_access_this_feature": "You have no permission to access this feature.", "paper_size": "Paper Size", "80_mm": "80 mm", "58_mm": "58 mm", "print_invoice": "Print Invoice", "ip_address": "IP Address", "port": "Port", "scheduled_order_time:": "Scheduled order time:", "payment_method": "Payment Method", "item_info": "Item Info", "qty": "Qty", "price:": "Price", "variation": "Variation", "add_variation": "Add Variation", "single": "Single", "multiple": "Multiple", "select_type": "Select Type", "option_name": "Option Name", "additional_price": "Additional Price", "add_new_option": "Add new Option", "required": "Required", "name": "Name", "add_new_variation": "Add New Variation", "enter_name_for_every_variation": "Enter name for every variation", "enter_min_max_for_every_multipart_variation": "Enter min-max for every multipart variation", "enter_option_name_for_every_variation": "Enter option name for every variation", "enter_option_price_for_every_variation": "Enter option price for every variation", "minimum_type_cant_be_less_then_1": "Minimum type can't be less then 1", "max_type_cant_be_less_then_minimum_type": "Max type can't be less then Minimum type", "max_type_length_should_not_be_more_then_options_length": "Max type length should not be more then options length", "multiple_select": "Multiple Select", "single_select": "Single Select", "optional": "Optional", "set_value_for_all_variation": "Set value for all variation", "you_cant_withdraw_more_then_1000000": "You can't withdraw more then 9,99,999", "access_denied": "For access this feature, you need MY STORE & STORE SETUP both module permission.", "thank_you": "Thank You", "update_order_amount": "Update Order Amount", "update_discount_amount": "Update Discount Amount", "discount_amount": "Discount Amount", "select_cancellation_reasons": "Select Cancellation Reasons", "you_did_not_select_any_reason": "You did not select any reason", "coupon": "Coupon", "are_you_sure_to_delete": "Are you sure to delete", "you_want_to_delete_this_coupon": "You want to delete this coupon", "code": "Code", "total_users": "Total users", "valid_until": "Valid until", "update_coupon": "Update Coupon", "add_coupon": "Add Coupon", "title": "Title", "coupon_type": "Coupon type", "default": "<PERSON><PERSON><PERSON>", "limit_for_same_user": "Limit for same user", "min_purchase": "Min Purchase", "start_date": "Start Date", "expire_date": "Expire Date", "max_discount": "Max Discount", "please_fill_up_your_coupon_title": "Please fill up your coupon title", "please_fill_up_your_coupon_code": "Please fill up your coupon code", "please_select_your_coupon_start_date": "Please select your coupon start date", "please_select_your_coupon_expire_date": "Please select your coupon expire date", "please_fill_up_your_coupon_discount": "Please fill up your coupon discount", "limit_for_same_user_cant_be_more_then_100": "Limit for same user cant be more then 100", "expense_report": "Expense Report", "search_with_order_id": "Search with order id", "your_search_box_is_empty": "Your search box is empty", "from": "From", "to": "To", "no_expense_found": "No Expense Found", "expense_type": "Expense Type", "no_coupon_found": "No coupon found", "update_now": "Update Now", "your_app_is_deprecated": "Your app is deprecated", "recommended": "Recommended", "discount_on_product": "Discount on product", "food_status_updated_successfully": "Food status updated successfully", "maximum_delivery_charge": "Maximum delivery charge", "minimum_charge_can_not_be_more_then_maximum_charge": "Minimum charge can not be more then maximum charge", "minimum_delivery_charge": "Minimum delivery charge", "total_user": "Total User", "limit": "Limit", "tax_included": "Tax Included", "no_reasons_available": "No reasons available", "rejected": "Rejected", "enter_maximum_delivery_fee": "Enter maximum delivery fee", "enter_processing_time": "Enter processing time", "please_provide_processing_time": "Please provide processing time", "enter_processing_time_in_minutes": "Enter processing time in minutes", "provide_store_information_to_proceed_next": "Provide store information to proceed next", "upload_store_logo": "Upload Store Logo", "upload_store_cover": "Upload Store Cover", "provide_owner_information_to_confirm": "Provide owner information to confirm!", "8_or_more_character": "8 or more character", "1_number": "1 number", "1_upper_case": "1 upper case", "1_lower_case": "1 lower case", "1_special_character": "1 special character", "organic": "Organic", "please_setup_the_marker_in_your_required_location": "please setup the marker in your required location", "select_zone": "Select zone", "delivery_instruction": "Delivery Instruction", "unavailable_item_note": "Unavailable Item Note", "cutlery": "Cutlery", "prescription_order": "Prescription Order", "select_module_type": "Select module type", "please_select_module_first": "Please select module first", "please_select_zone": "Please select Zone", "estimated_delivery_time": "Estimated Delivery Time", "this_item_will_be_shown_in_the_user_app_website": "This item will be shown in the user app website", "meta_title": "Meta Title", "meta_description": "Meta Description", "meta_key_word": "Meta Key Word", "maximum_order_quantity": "Maximum Order Quantity", "add_maximum_item_order_quantity": "Add maximum item order quantity", "complete_delivery": "Complete Delivery", "from_camera": "From Camera", "from_gallery": "From Gallery", "completed_after_delivery_picture": "Completed After Delivery Picture", "take_a_picture": "Take a Picture", "order_proof": "Order Proof", "wallet_pay": "Wallet Pay", "partial_payment": "Partial Payment", "did_not_receive_user_notification": "Didn’t receive user notification?", "resend_it": "Resend it", "enter_otp_number": "Enter OTP Number", "paid_by_wallet": "<PERSON><PERSON>", "due_amount": "Due Amount", "maximum_item_order_quantity_can_not_be_negative": "Maximum item order quantity can't be negative", "pending_item": "Pending Item", "pending_for_approval": "Pending for A<PERSON><PERSON>al", "edit_and_resubmit": "Edit and Resubmit", "general_information": "General Information", "price_information": "Price Information", "available_variation": "Available variation", "tags": "Tags", "product_unit": "Product Unit", "is_organic": "Is Organic", "unit_price": "Unit Price", "tax": "Tax", "size": "Size", "colour": "Colour", "new_product": "New Product", "update_request": "Update Request", "banner": "Banner", "banner_list": "Banner List", "redirection_url": "Redirection URL", "add_new_banner": "Add <PERSON> Banner", "add_banner": "Add Banner", "upload_banner": "Upload Banner", "redirection_url_link": "Redirection URL / Link", "enter_url": "Enter URL", "drag_drop_file_or_browse_file": "Drag & drop file or Browse file", "banner_images_ration_5:1": "Banner Images Ration 5:1", "image_format_maximum_size_2mb": "Image format : jpg, png, jpeg | Maximum size : 2MB", "announcement": "Announcement", "announcement_content": "Announcement Content", "type_announcement": "Type announcement", "publish": "Publish", "banner_added_successfully": "Banner added successfully", "enter_title": "Enter title", "banner_deleted_successfully": "Banner deleted successfully", "banner_updated_successfully": "Banner updated successfully", "update_banner": "Update Banner", "this_item_is_under_review": "This item is under review", "announcement_updated_successfully": "Announcement updated successfully", "enter_announcement": "Enter announcement", "this_feature_is_for_sharing_important_information_or_announcements_related_to_the_vendor": "This feature is for sharing important information or announcements related to the vendor.", "customer_will_see_these_banners_in_your_store_details_page_in_website_and_user_apps": "Customer will see these banners in your store details page in website and user apps", "note": "Note", "are_you_sure_to_delete_this_banner": "Are you sure to delete this banner?", "paid_by": "Paid by", "this_item_has_been_rejected": "This item has been rejected", "no_banner_found": "No banner found", "available_time": "Available Time", "disbursement": "Disbursement", "view_disbursement_history": "View Disbursement History", "disbursement_method_setup": "Disbursement Method Setup", "withdraw_methods": "Withdraw Methods", "set_default_method_successful": "Set default method successful", "default_method": "Default Method", "make_default": "Make Default", "no_method_found": "No Method Found", "pending_disbursements": "Pending Disbursements", "completed_disbursements": "Completed Disbursements", "canceled_transactions": "Cancelled Transactions", "disbursement_history": "Disbursement History", "no_history_available": "No history available", "add_withdraw_method": "Add Withdraw Method", "required_fields_can_not_be_empty": "Required fields can not be empty", "attention_please": "Attention Please !", "withdraw_methods_attention_message": "Please update at least one withdraw method and make it your default method. Otherwise admin cannot pay you disburse amount.", "set_default_withdraw_method": "Set Default Payment Method", "payment_information": "Payment Information", "you_payment_has_been_completed_your_will_receive_the_amount_within_7_day_please_wait_till_then": "You Payment has been completed. Your will receive the amount within 7 day. Please wait till then.", "disbursement_id": "Disbursement ID", "are_you_sure_to_delete_this_method": "Are you sure to delete this method?", "add_method": "Add Method", "pending_message": "All the pending disbursement requests that require admin’s action (complete/cancel).", "completed_message": "The amount of disbursement is completed.", "cancellation_message": "See all the canceled disbursement amounts here.", "pay_online": "Pay Online", "payment": "Payment", "do_you_want_to_cancel_this_payment": "Do you want to cancel this payment", "cancel_payment": "Cancel Payment", "your_payment_is_successfully_placed": "Your payment is successfully placed", "your_payment_is_not_done": "Your payment is not done", "okay": "Okay", "please_select_payment_method": "Please select payment method", "withdrawal_amount": "<PERSON><PERSON><PERSON><PERSON> Amount", "payable_amount": "Payable Amount", "pay_now": "Pay Now", "adjust_payments": "Adjust Payments", "wallet_adjustment_successfully": "Wallet Adjustment Successfully", "cash_adjustment": "Cash Adjustment", "cash_adjustment_description": "To adjust your Cash in Hand balance and Withdrawable Amount please click OK to confirm the adjustments", "withdraw_request": "Withdraw Request", "payment_history": "Payment History", "paid_via": "Paid via", "no_transaction_found": "No Transaction Found", "select_payment_method": "Select payment method", "add_successfully": "Add Successfully", "over_flow_block_warning_message": "Your limit to hold cash is exceeded. Your account will be suspended until you pay the due. You will not receive any new order request from now.", "over_flow_warning_message": "Looks like your limit to hold cash will be exceed soon. Please pay the due amount or other wise your account will be suspended if the amount acceed", "pay_the_due": "Pay the Due", "already_withdrawn": "Already Withdrawn", "transaction_history": "Transaction History", "pay_via_online": "Pay via Online", "faster_and_secure_way_to_pay_bill": "Faster and secure way to pay bill", "withdrawable_balance": "Withdrawable Balance", "Withdrawable Balance": "Withdrawable Balance", "currently_no_withdraw_method_available": "Currently no withdraw method available", "currently_there_are_no_payment_options_available_please_contact_admin_regarding_any_payment_process_or_queries": "Currently, there are no payment options available. Please contact admin regarding any payment process or queries.", "you_do_not_have_sufficient_balance_to_pay_the_minimum_payable_balance_is": "You don't have sufficient balance to pay. The minimum payable balance is", "back_press_again_to_exit": "Back press again to exit", "payment_method_deleted": "Payment method deleted", "please_enter_the_max_min_delivery_time": "Please enter the max & min delivery time", "enter_meta_title": "Enter meta title", "enter_meta_description": "Enter meta description", "please_upload_lower_size_file": "Please upload lower size file", "product_status_updated_successfully": "Product status updated successfully", "store_registration_successfully": "Store registration successfully", "the_product_will_be_published_once_it_receives_approval_from_the_admin": "The product will be published once it receives approval from the admin", "your_product_added_for_approval": "Your product added for approval", "sorry_cannot_view_this_conversation": "Sorry, cannot view this conversation.", "may_have_been_removed_from": "May have been removed from", "provide_valid_password": "Provide valid password", "enter_valid_url": "Enter valid URL", "upload_item_thumbnail_image": "Upload item thumbnail image", "minimum_delivery_time_can_not_be_empty": "Minimum delivery time can not be empty", "maximum_delivery_time_can_not_be_empty": "Maximum delivery time can not be empty", "time_unit_can_not_be_empty": "Time unit can not be empty", "extra_packaging_charge": "Extra Packaging Charge", "extra_packaging": "Extra packaging", "brand": "Brand", "prescription_required": "Prescription Required", "this_item_need_prescription_to_place_order": "This item need prescription to place order", "halal_item": "<PERSON><PERSON>", "this_item_is_halal": "This is halal item", "referral_discount": "Referral Discount", "enter_extra_packaging_amount_more_than_0": "Enter extra packaging amount more than 0", "customer_reviews": "Customer Reviews", "write_something_about_the_item": "Write something about the item", "write_something": "Write something", "review_reply": "Review Reply", "update_reply": "Update Reply", "write_your_reply_here": "Write your reply here", "send_reply": "Send Reply", "update_review": "Update Review", "reply_updated_successfully": "Reply updated successfully", "order": "Order", "view_reply": "View Reply", "give_reply": "Give Reply", "reviewer": "Reviewer", "search_by_order_id_item_or_customer": "Search by order id, item or customer", "store_info": "Store Info", "email_field_is_required": "Email field is required", "enter_valid_email_address": "Enter valid email address", "this_field_is_required": "This field is required", "minimum_password_is_8_character": "Minimum password is 8 character", "confirm_password_field_is_required": "Confirm password field is required", "write_store_name": "Write store name", "store_name_field_is_required": "Store name field is required", "store_logo": "Store Logo", "store_cover": "Store Cover", "upload_jpg_png_gif_maximum_2_mb": "Upload jpg, png, jpeg, gif maximum 2 MB", "please_place_the_marker_inside_the_zone": "Please place the marker inside the zone", "write_store_address": "Write store address", "store_address_field_is_required": "Store address field is required", "not_in_zone": "Not in zone", "store_preference": "Store Preference", "write_vat_tax_amount": "Write VAT Tax Amount", "please_provide_vat_tax_amount": "Please provide VAT Tax Amount", "store_vat_tax_field_is_required": "Store VAT Tax field is required", "owner_info": "Owner Info", "this_info_will_need_for_store_app_and_panel_login": "This info will need for store app and panel login", "write_first_name": "Write first name", "first_name_field_is_required": "First name field is required", "write_last_name": "Write last name", "last_name_field_is_required": "Last name field is required", "write_email": "Write email", "8+characters": "8 + characters", "password_field_is_required": "Password field is required", "select_time": "Select Time", "welcome_to": "Welcome to", "thanks_for_joining_us_your_registration_is_under_review_hang_tight_we_ll_notify_you_once_approved": "Thanks for joining us! Your registration is under review. Hang tight, we'll notify you once approved!", "registration_success": "Registration Success", "transaction_failed": "Transaction Failed", "subscription_success_message": "Thank you for your subscription purchase! Your payment was successfully processed. Please note that your subscription will be activated once it has been approved by our Admin Team.", "commission_base_success_message": "You've opted for our commission-based plan. <PERSON><PERSON> will review the details and activate your account shortly.", "continue_to_home_page": "Continue to Home Page", "sorry_your_transaction_can_not_be_completed_please_choose_another_payment_method_or_try_again": "Sorry, Your Transaction can't be completed. Please choose another payment method or try again.", "try_again": "Try Again", "you_are_one_step_away_choose_your_business_plan": "You are one step away! Choose your business plan", "choose_your_business_plan": "Choose your business plan", "commission_base": "Commission Base", "profit_margin": "<PERSON><PERSON>", "subscription_base": "Subscription Base", "store_will_pay": "Store will pay", "commission_to": "commission to", "from_each_order_You_will_get_access_of_all": "from each order. You will get access of all the features and options in vendor panel , app and interaction with user.", "store_will_set_base_prices_and_mrp_admin_can_override_mrp_vendor_wallet_calculations_use_base_price_only_you_will_get_access_of_all": "Store will set base prices and MRP. Admin can override MRP. Vendor wallet calculations use base price only. You will get access of all the features and options in vendor panel, app and interaction with user.", "run_store_by_purchasing_subscription_packages": "Run store by puchasing subsciption packages. You will have access the features of in vendor panel , app and interaction with user according to the subscription packages.", "no_package_available": "No package available", "continue_with": "Continue with", "days_free_trial": "free trial", "are_you_sure_to_go_back": "Are you sure to go back?", "your_registration_has_been_completed_successfully": "Your registration has been completed successfully", "location_info": "Location Info", "store_information": "Store Information", "back": "Back", "choose_subscription_package": "Choose Subscription Package", "my_subscription": "My Subscription", "subscription_details": "Subscription Details", "billing_details": "Billing Details", "next_billing_date": "Next Billing Date", "total_bill": "Total Bill", "number_of_uses": "Number of Uses", "change_or_renew_subscription_plan": "Change/Renew Subscription Plan", "cancel_subscription": "Cancel Subscription", "package_overview": "Package Overview", "transaction_id": "Transaction ID", "transaction_successful": "Transaction Successful", "purchase_status": "Purchase Status", "package_name": "Package Name", "time": "Time", "validity": "Validity", "change_subscription_plan": "Change Subscription Plan", "renew_or_shift_your_plan_to_get_better_experience": "Renew or shift your plan to get better experience!", "enter_email": "<PERSON><PERSON>", "days_left_in_free_trial": "Days left \nin free trial", "choose_plan": "Choose <PERSON>", "your_free_trial_has_been_ended": "Your Free Trial Has Been Ended", "purchase_subscription_message": "Purchase a subscription plan or contact with the admin to settle the payment and unblock the access to service.", "all_access_to_service_has_been_blocked_due_to_no_active_subscription": "All Access to service has been blocked due to no active subscription.", "your_business_plan_not_setup_yet": "Your business plan not setup yet", "renew_subscription_plan": "Renew Subscription Plan", "bill_status": "Bill Status", "review_id": "Review ID", "view": "View", "write_order_id_food_name_for_search": "Write order id, food name for search", "max_order": "Max Order", "max_product": "Max Product", "self_delivery": "Self Delivery", "mobile_app": "Mobile App", "review": "Review", "subscription_not_available_please_contact_with_admin": "Subscription not available. Please contact with admin", "left": "left", "pos_access": "POS Access", "mobile_app_access": "Mobile App Access", "attention_text_1": "Your Subcription Tiral Preiod is Ending Soon. Please Renew Before ", "attention_text_2": ". Otherwise All Your Activities will Turn Off Automatically After That.", "renew": "<PERSON>w", "shift_this_plan": "Shift in this plan", "shift_to_new_subscription_plan": "Shift to New Subscription Plan", "shift_subscription_plan": "Shift Subscription Plan", "for": "For", "package": "Package", "thank_you_for_transaction_with": "Thank you for transaction with", "in": "In", "renewed": "Renewed", "migrated": "Migrated", "free_trial": "Free Trial", "purchased": "Purchased", "you_denied_location_permission": "You denied location permission", "close": "Close", "settings": "Settings", "you_have_to_allow": "You have to allow", "expired": "Expired", "current_plan": "Current plan", "are_you_sure": "Are You Sure?", "do_you_want_to_cancel_this_subscription": "Do you want to cancel this subscription?", "you_want_to_migrate_to_commission": "You want to migrate to commission.", "you_have_no_available_subscription": "Your package does not include this section", "migrate": "Migrate", "package_expired": "Package Expired", "please_choose_a_business_plan": "Please choose a business plan", "please_enable_your_location_and_bluetooth_in_your_system": "Please enable your location and bluetooth in your system", "no_thermal_printer_connected": "No thermal printer connected", "paired_bluetooth": "Paired Bluetooth", "connected": "Connected", "click_to_connect": "Click to Connect", "are_you_sure_you_want_to_switch_to_this_plan": "Are you sure you want to switch to this plan?", "you_are_about_to_downgrade_your_plan_after_subscribing_to_this_plan_your_oldest": "You are about to downgrade your plan. After subscribing to this plan, your oldest", "items_will_be_inactivated": "items will be inactivated.", "your_registration_not_setup_yet": "Your registration not setup yet", "complete": "Complete", "attention": "Attention", "subscription_cancel_successfully": "Subscription cancel successfully", "if_you_cancel_the_subscription_after": "If you cancel the subscription, after", "days_you_will_no_longer_be_able_to_run_the_business_before_subscribe_to_a_new_plan": "days you will no longer be able to run the business before subscribe to a new plan.", "you_have_not_sufficient_balance_on_you_wallet_please_add_money_to_your_wallet_to_purchase_the_packages": "You have not sufficient balance on you wallet! please add money to your wallet to purchase the packages", "products_upload": "Products Upload", "subscription_payment_successfully": "Subscription payment successfully", "successfully_switched_to_commission_based_plan": "Successfully switched to commission based plan", "congratulations": "Congratulations", "change_business_plan": "Change Business Plan", "commission_base_plan": "Commission Base Plan", "my_business_plan": "My Business Plan", "plan_details": "Plan Details", "shift_to_new_business_plan": "Shift to New Business Plan", "sl": "SL", "your_package_is_expired": "Your Package is Expired", "renew_or_change_your_subscription_plan_to_unblock_the_access_to_service": "Renew or change your subscription plan to unblock the access of all service", "you_will_get": "You will get", "to_your_wallet_for_remaining": "to your wallet for remaining", "days_subscription_plan": "days subscription plan", "loading": "Loading...", "advertisements": "Advertisements", "ads_details": "Ads Details", "ads_id": "Ads ID", "ads_created": "Ads Created", "duration": "Duration", "ads_type": "Ads Type", "payment_status": "Payment Status", "service_info": "Service Info", "denied_note": "Denied Note", "pause_note_title": "Pause Note", "video": "Video", "profile_image": "Profile Image", "cover_image": "Cover Image", "edit_ads": "Edit Ads", "delete_ads": "Delete Ads", "confirm_delete_dialog_title": "Confirm Ad Deletion", "confirm_delete_dialog_description": "Deleting this ad will remove it permanently. Are you sure you want to proceed?", "advertisement_list": "Advertisement List", "edit_and_resubmit_ads": "Edit and Resubmit Ads", "view_ads": "View Ads", "can't_delete_dialog_title": "You can't delete the ad", "can't_delete_dialog_description": "Your ad is running. To delete this ad from the list, please change its status first. Once the status is updated, you can proceed with deletion", "pause_dialog_title": "Are you sure you want to Pause the request", "pause_dialog_description": "This ad will be pause and not show in the app or web", "resume_ads": "Resume Ads", "resume_dialog_title": "Are you sure you want to resume the request", "resume_dialog_description": "This ad will be run again and will show in the app or web", "copy_ads": "Copy Ads", "ads_placed": "Ads Placed", "new_advertisement": "New Advertisement", "update_advertisement": "Update Advertisement", "category_info": "Category Info", "enter_validity": "Enter validity", "enter_a_valid_date_range": "Enter a valid date range", "enter_description": "Enter description", "show_review_ratings": "Show Review Ratings", "rating": "Rating", "upload_files": "Upload Files", "click_to_upload_ads_video": "Click to Upload \n Ads Video", "enter_video": "Enter video", "video_ratio_text": "Maximum 5 MB \n Supports: MP4, WEBM, MKV", "click_to_upload_cover_image": "Click to Upload \n Cover Image", "click_to_upload_profile_image": "Click to Upload \n Profile Image", "cover_ratio_text": "Ratio: 2:1 \n Format: jpg, jpeg, png, webp \n Size: Max 2 MB", "profile_ratio_text": "Ratio: 1:1 \n Format: jpg, jpeg, png, webp \n Size: Max 1 MB", "enter_profile_image": "Enter profile image", "enter_cover_image": "Enter cover image", "reset": "Reset", "update_ads": "Update Ads", "create_ads": "Create Ads", "preview": "Preview", "want_to_get_highlighted": "Want To Get Highlighted?", "create_ads_to_get_highlighted_on_the_app_and_web_browser": "Create ads to get highlighted on the app and web browser", "ads_created_successfully": "Ad Created Successfully!", "congratulation_description": "Congratulations on creating your ad! It's now awaiting approval. To finalize the process & make payment arrangements, please contact our Admin directly. We look forward to helping you boost your visibility & reach more customers.", "cancel_ads": "Cancel Ads", "pause_ads": "Pause Ads", "cancelation_note": "Cancellation Note", "pause_note": "Pause Note...", "enter_cancellation_note": "Enter cancellation note", "enter_paused_note": "<PERSON><PERSON> paused note", "not_now": "Not Now", "upload_file": "Upload File", "uh_oh_You_didnt_created_any_advertisement_yet": "Uh oh! You didn’t created any advertisement yet!", "by_creating_advertisement": "By Creating Advertisement you can showcase your foods or profile to a wider audience through targeted ad campaigns.", "ads_preview": "Ads Preview", "video_promotion": "Video Promotion", "store_promotion": "Store Promotion", "video_size_greater_than": "Video size greater than 50 MB", "profile_size_greater_than": "Profile image is greater than 2 MB", "cover_image_size_greater_than": "Cover image is greater than 2 MB", "advertisement_deleted_successfully": "Advertisement deleted successfully", "advertisement_not_deleted": "Advertisement not deleted", "in_the_customer_app_and_websites": "In the customer app & websites", "choose_your_language": "Choose Your Language", "choose_your_language_to_proceed": "Choose your language to proceed", "language_updated_successfully": "Language updated successfully", "you_want_to_disable_announcement": "You want to disable announcement?", "you_want_to_enable_announcement": "You want to enable announcement?", "you_want_to_enable_notification": "You want to enable notification?", "you_want_to_disable_notification": "You want to disable notification?", "no_data_available": "No data available", "ads_title": "Ads Title", "you_can_not_close_the_store_because_you_already_have_running_orders": "You can't close the store. Because you already have ongoing orders", "you_can_not_close_the_restaurant_because_you_already_have_running_orders": "You can't close the restaurant. Because you already have ongoing orders", "advertisement_paused_successfully": "Advertisement Paused Successfully", "advertisement_resume_successfully": "Advertisement Resumed Successfully", "insert_language_wise_item_name_and_description": "Insert language wise item name and description", "item_setup": "<PERSON><PERSON>", "nutrition": "Nutrition", "allergic_ingredients": "Allergic Ingredients", "is_it_halal": "Is It Halal?", "availability": "Availability", "price_info": "Price Info", "tag": "Tag", "generic_name": "Generic Name", "you_can_select_or_add_maximum_5_nutrition": "You can select or add maximum 5 nutrition", "you_can_select_or_add_maximum_5_allergic_ingredients": "You can select or add maximum 5 allergic ingredients", "you_can_select_or_add_maximum_5_generic_name": "You can select or add maximum 5 generic name", "discount_cannot_be_more_than_100": "Discount can't be more than 100%", "discount_cannot_be_more_than_price": "Discount can't be more than price", "discount_cannot_be_more_than_base_price": "Discount can't be more than base price", "is_basic_medicine": "Is Basic Medicine?", "discount_cant_be_more_then_minimum_variation_price": "Discount can't be more then minimum variation price", "specify_the_ingredients_of_the_item_which_can_make_a_reaction_as_an_allergen": "Specify the ingredients of the item which can make a reaction as an allergen", "specify_the_necessary_keywords_relating_to_energy_values_for_the_item": "Specify the necessary keywords relating to energy values for the item", "specify_the_medicine_active_ingredient_that_makes_it_work": "Specify the medicine's active ingredient that makes it work", "total_quantity": "Total Quantity", "update_stock": "Update Stock", "minimum_stock_for_warning": "Minimum Stock For Warning", "warning": "Warning!", "more": "+ more", "products_are_low_on_stock": "products are low on Stock", "view_details": "View Details", "low_stock_products": "Low Stock Products", "low_stock": "Low Stock", "suitable_for": "Suitable For", "minimum_stock_for_warning_tooltip": "When the stock of a product reaches its minimum value that you’ve set, you will receive a warning to update the stock. Additionally, these products will appear in the Admin's Low Stock list.", "type_and_click_add_button": "Type and click add button", "no_item_found": "No item found", "product_is_low_on_stock": "Product is low on stock", "stock_cannot_be_zero": "Stock can't be zero", "you_have_no_business_plan": "You have no business plan", "chose_a_business_plan_from_the_list_so_that_you_get_more_options_to_join_the_business_for_the_growth_and_success": "Choose a business plan from the list so that you get more options to join the business for the growth and success", "chose_business_plan": "Choose Business Plan", "chose_a_business": "Choose a business", "chose_a_business_plan_to_get_better_experience": "Choose a business plan to get better experience", "choose_subscription_plan": "Choose Subscription Plan", "Approved": "Approved", "Denied": "Denied", "Pending": "Pending", "provider_temporary_off": "Provider Temporary Off", "trip_id": "Trip ID", "estimated": "Estimated", "trip_history": "Trip History", "home": "Home", "trips": "Trips", "menu": "<PERSON><PERSON>", "see_reviews": "See Reviews", "all_vehicle": "All Vehicle", "vehicle_list": "Vehicle List", "edit_business": "Edit Business", "business_status": "Business Status", "when_business_status_on_you_will_get_trip_request": "When business status ON you will get trip request", "general_info": "General Info", "type_provider_name": "Type Provider Name", "type_provider_address": "Type Provider Address", "vehicle_information": "Vehicle Information", "scheduled_trip": "Scheduled Trip", "when_on_vendor_will_get_scheduled_trip": "When ON Vendor will get scheduled trip", "vehicle_new_tag": "Vehicle New Tag", "extra_service_charge": "Extra Service Charge", "when_on_vendor_will_get_extra_charge": "When ON Vendor will get Extra charge", "type_service_name": "Type Service Name", "extra_charge_amount": "Extra Charge Amount", "ex_10": "Ex: 10", "gst_number": "GST Number", "approx_pickup_time": "Approx Pickup Time", "min_20": "Min: 20", "max_30": "Max: 30", "select_an_option": "Select an option", "please_select_an_option": "Please select an option", "provider_active_time": "Provider Active Time", "business_logo": "Business Logo", "image_format_and_ratio_for_logo": "JPG, JPEG, PNG Less Than 1MB (Ratio 1:1)", "cover": "Cover", "image_format_and_ratio_for_cover": "JPG, JPEG, PNG Less Than 1MB (Ratio 2:1)", "click_to_upload": "Click to Upload", "or_drag_and_drop": "Or Drag and Drop", "vehicle_details": "Vehicle Details", "activity": "Activity", "new_tag": "New Tag", "trip_type": "Trip Type", "hourly": "Hourly", "distance_wise": "Distance Wise", "newly_arrived": "Newly Arrived", "add_new_vehicle": "Add New Vehicle", "type_vehicle_name": "Type Vehicle Name", "type_vehicle_description": "Type Vehicle Description", "vehicle_thumbnail": "Vehicle Thumbnail", "vehicle_images": "Vehicle Images", "pricing_and_discount": "Pricing & Discount", "enter_price": "Enter Price", "hourly_price": "Hourly Price", "distance_wise_price": "Distance Wise Price", "vehicle_type": "Vehicle Type", "engine_capacity_cc": "Engine Capacity (cc)", "engine_power_hp": "Engine Power (hp)", "fuel_type": "Fuel Type", "transmission_type": "Transmission Type", "setting_capacity": "Setting Capacity", "air_condition": "Air Condition", "extra_feature": "Extra Feature", "identity": "Identity", "add_new": "Add New", "vin_number": "VIN Number", "enter_vin_number": "Enter VIN Number", "license_plate_number": "License Plate Number", "enter_license_plate_number": "Enter License Plate Number", "type_and_enter_related_tags": "Type & Enter Related Tags", "search_tag": "Search Tag", "vehicle_other_documents": "Vehicle Other Documents", "vehicle_license": "Vehicle License", "vehicle_doc_format": "PDF, DOC, IMAGE Less Than 2 MB", "vehicle": "Vehicle", "cancel_booking": "Cancel Booking", "accept": "Accept", "trip": "Trip", "trip_details": "Trip Details", "rent_type": "Rent Type", "additional_notes": "Additional Notes", "bill_details": "<PERSON>", "edit_trip_cost": "Edit <PERSON>st", "payment_received": "Payment Received?", "confirm_payment_message": "Are you sure the customer's payment has been successfully received?", "edit_trip_details": "Edit Trip <PERSON>ails", "trip_location": "Trip Location", "pickup_time": "Pickup Time", "pickup_now": "Pickup Now", "estimated_time": "Estimated Time", "selected_vehicle": "Selected Vehicle", "action": "Action", "vehicles_list": "Vehicles List", "vin": "VIN", "license": "License", "driver": "Driver", "assign_driver": "Assign Driver", "vehicle_activity_updated_successfully": "Vehicle Activity Updated Successfully", "vehicle_new_tag_updated_successfully": "Vehicle New Tag Updated Successfully", "ac": "AC", "non_ac": "Non AC", "vehicle_deleted_successfully": "Vehicle Deleted Successfully", "no_vehicle_found": "No Vehicle Found", "family": "Family", "luxury": "Luxury", "affordable": "Affordable", "executives": "Executives", "compact": "Compact", "midsize": "Midsize", "full_size": "Full-Size", "octan": "Octan", "diesel": "Diesel", "cng": "CNG", "petrol": "Petrol", "electric": "Electric", "jet_fuel": "Jet Fuel", "automatic": "Automatic", "manual": "Manual", "continuously_variable": "Continuously Variable", "dual_clutch": "Dual Clutch", "semi_automatic": "Semi-Automatic", "vehicle_updated_successfully": "Vehicle Updated Successfully", "vehicle_added_successfully": "Vehicle Added Successfully", "add_vehicle": "Add Vehicle", "brands": "Brands", "no_brand_found": "No Brand Found", "basic_info": "Basic Info", "basic_setup": "Basic Setup", "driver_list": "Driver List", "driver_available": "Driver Available", "driver_details": "Driver Details", "total_trip": "Total Trip", "completed": "Completed", "business_setup_updated_successfully": "Business Setup Updated Successfully", "upload_business_logo": "Upload Business Logo", "upload_cover_image": "Upload Cover Image", "enter_gst_number": "Enter GST Number", "enter_min_time": "Enter Minimum Time", "enter_max_time": "Enter Maximum Time", "select_time_type": "Select Time Type", "drivers": "Drivers", "no_driver_found": "No Driver Found", "add_new_driver": "Add New Driver", "type_first_name": "Type First Name", "type_last_name": "Type Last Name", "type_your_email": "Type Your Email", "type_your_phone_number": "Type Your Phone Number", "driver_info": "Driver Info", "same_model_multiple_vehicles": "Same Model Multiple Vehicles", "identity_info": "Identity Info", "type_identity_number": "Type Identity Number", "identity_image": "Identity Image", "enter_driver_first_name": "Enter Driver First Name", "enter_driver_last_name": "Enter Driver Last Name", "enter_driver_phone_number": "Enter Driver Phone Number", "upload_driver_image": "Upload Driver Image", "select_identity_type": "Select Identity Type", "upload_identity_image": "Upload Identity Image", "driver_updated_successfully": "Driver Updated Successfully", "driver_added_successfully": "Driver Added Successfully", "update_driver": "Update Driver", "driver_deleted_successfully": "Driver Deleted Successfully", "update_vehicle": "Update Vehicle", "instant": "Instant", "hrs": "hrs", "km": "km", "no_trip_found": "No Trip Found", "ongoing": "Ongoing", "payment_failed": "Payment Failed", "are_you_sure_to_open_service": "Are you sure want to change the active status to open for this service?", "are_you_sure_to_close_service": "Are you sure want to change the active status to close for this service temporary?", "seats": "Seats", "car_no": "Car No", "driver_status_updated_successfully": "Driver Status Updated Successfully", "search_by_trip_id_vehicle_or_customer": "Search by trip id, vehicle or customer", "write_trip_id_vehicle_name_for_search": "Write trip id, vehicle name for search", "search_by_driver_name_phone_email": "Search by driver name, phone, email", "write_driver_name_phone_email_for_search": "Write driver name, phone, email for search", "select_pick_zone": "Select Pick Zone", "zone_already_added_please_select_another": "This zone already added. Please select another", "total_completed_trips": "Total Completed Trips", "total_trips": "Total Trips", "no_additional_notes": "No Additional Notes Available", "trip_cost": "Trip Cost", "trip_discount": "Trip Discount", "vat": "VAT", "service_fee": "Service Fee", "total": "Total", "due_payment": "Due Payment", "no_driver_assign_yet": "No Driver Assign Yet", "vehicle_need_to_assign_driver": "Vehicle need to assign driver", "select_driver": "Select Driver", "assigned_driver": "Assigned Driver", "change": "Change", "see_less": "See Less", "see_more": "See More", "assign_vehicle": "Assign Vehicle", "assign_licence_number": "Assign Licence Number", "assign_the_licence_number_of_the_vehicles_that_you_want_to_send_in_this_trip": "Assign the licence number of the vehicles that you want to send in this trip", "vehicle_license_number": "Vehicle License Number", "start_from": "Start From", "estimated_distance": "Estimated Distance", "fair": "Fair", "apply_filter": "Apply Filter", "cooling": "Cooling", "air_conditioned": "Air Conditioned", "non_air_conditioned": "Non Air Conditioned", "filter_by": "Filter by", "vehicle_assigned_successfully": "Vehicle Assigned Successfully", "select_any_of": "Select any of", "please_select_at_least_one_vehicle": "Please select at least one vehicle", "you_can_not_select_more_than": "You can't select more than", "vehicles": "vehicles", "driver_already_assigned": "This driver is already assigned. Please select another driver", "driver_assigned_successfully": "Driver Assigned Successfully", "actual_fair": "Actual Fair", "quantity_should_be_less_than_previous": "Quantity should be less than previous", "location": "Location", "map": "Map", "enter_pickup_location": "Enter Pickup Location", "enter_destination_location": "Enter Destination Location", "set_date_and_time": "Set Date & Time", "edited": "Edited", "swipe_to_ongoing": "Swipe to Ongoing", "swipe_to_complete": "Swipe to Complete", "swipe_to_confirm_payment": "Swipe to Confirm Payment", "payment_status_update_failed": "Payment Status Update Failed", "driver_delete_confirmation": "Do you want to delete this drive? If you delete, the driver info will permanently deleted from your system.", "search_with_trip_id": "Search with Trip ID", "enter_provider_name_and_address_for_english": "Enter provider name and address for English", "select_duration": "Select Duration", "upload_vehicle_thumbnail_image": "Upload Vehicle Thumbnail Image", "upload_vehicle_images": "Upload Vehicle Images", "system_est_fair": "System Est. Fair", "select_trip_type": "Select Trip Type", "enter_hourly_price": "Enter Hourly Price", "enter_distance_price": "Enter Distance Price", "enter_discount": "Enter Discount", "select_discount_type": "Select Discount Type", "select_brand": "Select Brand", "select_category": "Select Category", "select_vehicle_type": "Select Vehicle Type", "enter_setting_capacity": "Enter Setting Capacity", "enter_engine_capacity": "Enter Engine Capacity", "enter_engine_power": "Enter Engine Power", "select_fuel_type": "Select Fuel Type", "select_transmission_type": "Select Transmission Type", "upload_vehicle_license": "Upload Vehicle License", "vat_included": "VAT Included", "business_plan": "Business Plan", "chatting": "Chatting", "report": "Report", "trip_details_updated_successfully": "Trip Details Updated Successfully", "new_trip_booked": "A new trip has been booked. Please take a look.", "vendor_owner": "Vendor Owner", "vendor_employee": "Vendor Employee", "vendor": "<PERSON><PERSON><PERSON>", "vendor_registration": "Vendor Registration", "vendor_info": "Vendor Info", "write_vendor_name": "Write Vendor Name", "vendor_name": "Vendor Name", "vendor_name_field_is_required": "Vendor name field is required", "vendor_logo": "<PERSON><PERSON><PERSON>", "upload_vendor_logo": "Upload <PERSON><PERSON><PERSON>", "vendor_cover": "<PERSON><PERSON><PERSON>", "upload_vendor_cover": "Upload Vendor Cover", "vendor_preference": "Vendor Preference", "vendor_vat_tax_field_is_required": "Vendor VAT Tax field is required", "estimated_pickup_time_time": "Estimated Pickup Time", "minimum_pickup_time_can_not_be_empty": "Minimum pickup time can not be empty", "maximum_pickup_time_can_not_be_empty": "Maximum pickup time can not be empty", "maximum_pickup_time_can_not_be_smaller_then_minimum_pickup_time": "Maximum pickup time can not be smaller then minimum pickup time", "this_info_will_need_for_vendor_app_and_panel_login": "This info will need for vendor app and panel login", "vendor_will_pay": "<PERSON><PERSON><PERSON> will pay", "run_vendor_by_purchasing_subscription_packages": "Run vendor by purchasing subscription packages. You will have access the features of in vendor panel , app and interaction with user according to the subscription packages.", "enter_vendor_name": "Enter vendor name", "select_vendor_logo": "Select vendor logo", "select_vendor_cover_photo": "Select vendor cover photo", "enter_vendor_address": "Enter vendor address", "set_vendor_location": "Set vendor location", "provide_vendor_information_to_proceed_next": "Provide vendor information to proceed next", "trip_status_updated": "Trip Status Updated", "reply_sent_successfully": "<PERSON><PERSON> Successfully", "write_your_reply": "Write Your Reply", "custom": "Custom", "vehicle_delete_confirmation": "Do you want to delete this vehicle? If you delete, the vehicle info will permanently deleted from your system.", "you_can_not_add_more_than": "You can't add more than", "please_fill_all_quantity_fields": "Please fill all quantity fields", "please_fill_all_fair_fields": "Please fill all fair fields", "quantity_can_not_be_zero": "Quantity can not be zero", "fair_can_not_be_zero": "Fair can not be zero", "search_by_vehicle_name": "Search by vehicle name", "write_vehicle_name_for_search": "Write vehicle name for search", "booked_at": "Booked At", "unlimited": "Unlimited", "hourly_price_cannot_be_zero": "Hourly price can not be 0", "distance_price_cannot_be_zero": "Distance price can not be 0", "please_select_pickup_zone": "Please select pickup zone", "max_trip": "<PERSON>", "max_vehicle": "Max Vehicle", "coupon_status_updated": "Coupon Status Updated", "vehicle_upload": "Vehicle Upload", "you_cannot_select_before_current_time": "You can't select before current time", "notification_is_disabled_please_allow_notification": "Notification is disabled. Please allow notification", "for_better_performance_allow_notification_to_run_in_background": "For better performance, allow notification to run in the background", "system_notification": "System Notification", "background_notification": "Background Notification", "allow": "Allow", "disable": "Disable", "to_run_notification_in_background": "to run notification in background?", "you_will_be_able_to_get_order_notification_even_if_you_are_not_in_the_app": "You will be able to get order notification even if you are not in the app", "will_run_notification_service_in_the_background_always": "will run notification service in the background always", "will_not_run_notification_service_in_the_background_always": "will not run notification service in the background always", "notification_will_always_send_alert_from_the_background": "Notification will always send alert from the background", "notification_will_not_always_send_alert_from_the_background": "Notification will not always send alert from the background", "make_sure_to_enable_app_notifications_first": "Make sure to enable app notifications first", "per_day": "Per Day", "enter_per_day_price": "Enter Per Day Price", "per_day_price_cannot_be_zero": "Per Day Price can not be 0", "per_day_price": "Per Day Price", "day_wise": "Day Wise", "reports": "Reports", "tax_report": "Tax Report", "total_orders": "Total Orders", "total_order_amount": "Total Order Amount", "total_tax_amount": "Total Tax Amount", "food_type": "Food Type", "halal_tag": "Halal Tag", "basic_medicine": "Basic Medicine", "food_variation": "Food Variation", "select_vat_tax": "Select VAT/Tax", "vat_tax_already_added_please_select_another": "VAT/Tax already added. Please select another", "new_option": "New Option", "new_variation": "New Variation", "selection_option": "Selection Option", "required_this_variation": "Required this variation", "variation_name": "Variation Name", "options_selection_type": "Options Selection Type", "single_selection": "Single Selection", "multi_selection": "Multi Selection", "thumbnail_image_format": "Upload jpg, png, jpeg, \nmaximum 2 MB", "update_addon": "Update Addon", "add_addons": "<PERSON><PERSON>", "min_selection": "Min Selection", "max_selection": "Max Selection", "enter_vehicle_data_for_english": "Enter vehicle name and description for English language", "select_addon_category": "Select Addon Category", "you_want_to_delete_this_addon": "You want to delete this addon?", "you_want_to_delete_this_item_request": "You want to delete this item request?", "yes_delete": "Yes, Delete", "tin_certificate": "TIN Certificate", "no_tax": "No Tax", "taxpayer_identification_number_tin": "Taxpayer Identification Number (TIN)", "tin": "TIN", "business_tin": "Business TIN", "vendor_tin_field_is_required": "Vendor TIN field is required", "enter_tin": "Enter TIN", "select_tin_expire_date": "Select TIN Expire Date", "upload_tin_certificate": "Upload TIN Certificate", "select_date": "Select Date", "view_and_track_your_business_expenses_in_detail": "View and track your business expenses in detail.", "view_detailed_tax_calculations_and_payment_records": "View detailed tax calculations and payment records.", "no_tax_report_found": "No tax report found", "reject_note": "Reject Note", "vat_tax_inc": "(Vat/Tax Incl.)", "orders": "Orders"}